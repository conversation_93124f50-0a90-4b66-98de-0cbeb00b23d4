"""
Smart Briefing Generation System
================================

Advanced system for generating personalized news briefings, topic deep dives,
comparative analysis, and executive summaries.
"""

import json
import statistics
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import logging

logger = logging.getLogger(__name__)

class SmartBriefingGenerator:
    """
    Advanced briefing generation system for personalized news summaries.
    Creates daily/weekly briefings, topic analyses, and executive reports.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the smart briefing generator."""
        self.config = self._load_config(config_path)
        self.briefing_templates = self._build_briefing_templates()
        self.analysis_frameworks = self._build_analysis_frameworks()
        self.executive_formats = self._build_executive_formats()
        
    def _load_config(self, config_path: Optional[str]) -> Dict:
        """Load briefing generation configuration."""
        default_config = {
            "daily_briefing_length": "medium",  # short, medium, long
            "weekly_briefing_length": "long",
            "max_articles_per_topic": 5,
            "min_articles_for_trend": 3,
            "executive_summary_length": 200,
            "include_sentiment_analysis": True,
            "include_market_impact": True,
            "include_recommendations": True,
            "personalization_level": "medium",
            "briefing_styles": {
                "executive": "formal",
                "daily": "professional",
                "weekly": "comprehensive"
            }
        }
        
        if config_path:
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
            except Exception as e:
                logger.warning(f"Could not load config from {config_path}: {e}")
                
        return default_config
    
    def _build_briefing_templates(self) -> Dict[str, str]:
        """Build briefing templates for different formats."""
        return {
            "daily_header": "# Daily News Briefing - {date}\n\n## Executive Summary\n{executive_summary}\n\n",
            "weekly_header": "# Weekly News Analysis - {date_range}\n\n## Week in Review\n{week_summary}\n\n",
            "topic_section": "## {topic_title}\n{topic_summary}\n\n**Key Articles:**\n{article_list}\n\n**Analysis:** {analysis}\n\n",
            "trend_section": "## Trending Topics\n{trending_analysis}\n\n",
            "market_section": "## Market Impact Analysis\n{market_analysis}\n\n",
            "recommendation_section": "## Recommendations\n{recommendations}\n\n",
            "footer": "---\n*Generated by AI News Assistant on {timestamp}*\n*Based on {article_count} articles from {source_count} sources*"
        }
    
    def _build_analysis_frameworks(self) -> Dict[str, Dict]:
        """Build analysis frameworks for different content types."""
        return {
            "financial": {
                "key_metrics": ["market_impact", "sector_analysis", "sentiment_trend"],
                "focus_areas": ["earnings", "mergers", "regulatory_changes", "market_movements"],
                "analysis_depth": "detailed"
            },
            "technology": {
                "key_metrics": ["innovation_impact", "adoption_potential", "competitive_landscape"],
                "focus_areas": ["product_launches", "ai_developments", "cybersecurity", "startups"],
                "analysis_depth": "technical"
            },
            "politics": {
                "key_metrics": ["policy_impact", "stakeholder_analysis", "public_sentiment"],
                "focus_areas": ["legislation", "elections", "international_relations", "governance"],
                "analysis_depth": "comprehensive"
            },
            "healthcare": {
                "key_metrics": ["clinical_impact", "regulatory_status", "market_potential"],
                "focus_areas": ["drug_approvals", "clinical_trials", "public_health", "innovation"],
                "analysis_depth": "scientific"
            }
        }
    
    def _build_executive_formats(self) -> Dict[str, str]:
        """Build executive summary formats."""
        return {
            "bullet_points": "• {point}\n",
            "numbered_list": "{number}. {point}\n",
            "paragraph": "{content}\n\n",
            "key_insights": "**Key Insight:** {insight}\n\n"
        }
    
    def generate_daily_briefing(self, articles: List[Dict], 
                               user_preferences: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Generate a daily news briefing.
        
        Args:
            articles: Articles from the current day
            user_preferences: User preferences for personalization
            
        Returns:
            Formatted daily briefing
        """
        # Filter and organize articles
        organized_articles = self._organize_articles_by_topic(articles)
        
        # Generate executive summary
        executive_summary = self._generate_executive_summary(articles, "daily")
        
        # Analyze trending topics
        trending_analysis = self._analyze_daily_trends(articles)
        
        # Generate market impact analysis
        market_analysis = self._generate_market_analysis(articles) if self.config["include_market_impact"] else ""
        
        # Generate recommendations
        recommendations = self._generate_recommendations(articles, "daily") if self.config["include_recommendations"] else ""
        
        # Build briefing content
        briefing_content = self._build_daily_briefing_content(
            organized_articles, executive_summary, trending_analysis, 
            market_analysis, recommendations
        )
        
        return {
            "briefing_type": "daily",
            "date": datetime.now().strftime("%Y-%m-%d"),
            "content": briefing_content,
            "metadata": {
                "articles_analyzed": len(articles),
                "topics_covered": len(organized_articles),
                "generated_at": datetime.now().isoformat(),
                "personalization_applied": user_preferences is not None
            }
        }
    
    def generate_weekly_briefing(self, articles: List[Dict], 
                                user_preferences: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Generate a weekly news briefing.
        
        Args:
            articles: Articles from the past week
            user_preferences: User preferences for personalization
            
        Returns:
            Formatted weekly briefing
        """
        # Organize articles by day and topic
        weekly_organization = self._organize_articles_by_week(articles)
        
        # Generate week summary
        week_summary = self._generate_week_summary(articles)
        
        # Analyze weekly trends
        weekly_trends = self._analyze_weekly_trends(articles)
        
        # Generate comparative analysis
        comparative_analysis = self._generate_comparative_analysis(articles)
        
        # Generate strategic insights
        strategic_insights = self._generate_strategic_insights(articles)
        
        # Build weekly briefing content
        briefing_content = self._build_weekly_briefing_content(
            weekly_organization, week_summary, weekly_trends,
            comparative_analysis, strategic_insights
        )
        
        return {
            "briefing_type": "weekly",
            "date_range": self._get_week_date_range(),
            "content": briefing_content,
            "metadata": {
                "articles_analyzed": len(articles),
                "days_covered": len(weekly_organization),
                "generated_at": datetime.now().isoformat(),
                "analysis_depth": "comprehensive"
            }
        }
    
    def generate_topic_deep_dive(self, topic: str, articles: List[Dict]) -> Dict[str, Any]:
        """
        Generate a deep dive analysis for a specific topic.
        
        Args:
            topic: Topic to analyze
            articles: Articles related to the topic
            
        Returns:
            Comprehensive topic analysis
        """
        # Filter articles for the topic
        topic_articles = self._filter_articles_by_topic(topic, articles)
        
        if not topic_articles:
            return {
                "topic": topic,
                "analysis": "No articles found for this topic",
                "metadata": {"articles_found": 0}
            }
        
        # Generate topic overview
        topic_overview = self._generate_topic_overview(topic, topic_articles)
        
        # Analyze topic evolution
        topic_evolution = self._analyze_topic_evolution(topic, topic_articles)
        
        # Identify key players
        key_players = self._identify_topic_key_players(topic, topic_articles)
        
        # Analyze sentiment trends
        sentiment_trends = self._analyze_topic_sentiment_trends(topic, topic_articles)
        
        # Generate future outlook
        future_outlook = self._generate_topic_future_outlook(topic, topic_articles)
        
        # Build deep dive content
        deep_dive_content = self._build_topic_deep_dive_content(
            topic, topic_overview, topic_evolution, key_players,
            sentiment_trends, future_outlook, topic_articles
        )
        
        return {
            "topic": topic,
            "analysis_type": "deep_dive",
            "content": deep_dive_content,
            "metadata": {
                "articles_analyzed": len(topic_articles),
                "analysis_generated_at": datetime.now().isoformat(),
                "depth": "comprehensive"
            }
        }
    
    def generate_comparative_analysis(self, topics: List[str], 
                                    articles: List[Dict]) -> Dict[str, Any]:
        """
        Generate comparative analysis between multiple topics.
        
        Args:
            topics: Topics to compare
            articles: Articles to analyze
            
        Returns:
            Comparative analysis report
        """
        topic_analyses = {}
        
        # Analyze each topic
        for topic in topics:
            topic_articles = self._filter_articles_by_topic(topic, articles)
            if topic_articles:
                topic_analyses[topic] = {
                    "article_count": len(topic_articles),
                    "sentiment_distribution": self._calculate_sentiment_distribution(topic_articles),
                    "key_themes": self._extract_topic_themes(topic_articles),
                    "market_impact": self._assess_topic_market_impact(topic_articles),
                    "trend_direction": self._assess_topic_trend_direction(topic_articles)
                }
        
        # Generate comparison insights
        comparison_insights = self._generate_comparison_insights(topic_analyses)
        
        # Build comparative analysis content
        comparative_content = self._build_comparative_analysis_content(
            topics, topic_analyses, comparison_insights
        )
        
        return {
            "analysis_type": "comparative",
            "topics_compared": topics,
            "content": comparative_content,
            "metadata": {
                "topics_analyzed": len(topic_analyses),
                "total_articles": sum(analysis["article_count"] for analysis in topic_analyses.values()),
                "generated_at": datetime.now().isoformat()
            }
        }
    
    def generate_executive_summary(self, articles: List[Dict], 
                                 focus_areas: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Generate executive summary for leadership consumption.
        
        Args:
            articles: Articles to summarize
            focus_areas: Specific areas to focus on
            
        Returns:
            Executive summary report
        """
        # Identify high-impact articles
        high_impact_articles = self._identify_high_impact_articles(articles)
        
        # Generate key insights
        key_insights = self._generate_key_insights(high_impact_articles, focus_areas)
        
        # Assess strategic implications
        strategic_implications = self._assess_strategic_implications(high_impact_articles)
        
        # Generate action items
        action_items = self._generate_action_items(high_impact_articles)
        
        # Create risk assessment
        risk_assessment = self._create_risk_assessment(high_impact_articles)
        
        # Build executive summary content
        executive_content = self._build_executive_summary_content(
            key_insights, strategic_implications, action_items, risk_assessment
        )
        
        return {
            "summary_type": "executive",
            "content": executive_content,
            "key_metrics": {
                "high_impact_articles": len(high_impact_articles),
                "key_insights_count": len(key_insights),
                "action_items_count": len(action_items),
                "risk_level": self._calculate_overall_risk_level(risk_assessment)
            },
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "articles_analyzed": len(articles),
                "focus_areas": focus_areas or ["general"]
            }
        }

    def _organize_articles_by_topic(self, articles: List[Dict]) -> Dict[str, List[Dict]]:
        """Organize articles by their main topics."""
        topic_articles = defaultdict(list)

        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                # Use the top topic for organization
                top_topic = article['smart_categorization']['topics'][0]['topic']
                topic_articles[top_topic].append(article)
            else:
                # Fallback to category
                category = article.get('category', 'General')
                topic_articles[category].append(article)

        return dict(topic_articles)

    def _generate_executive_summary(self, articles: List[Dict], briefing_type: str) -> str:
        """Generate executive summary for briefing."""
        if not articles:
            return "No significant news to report."

        # Count articles by sentiment
        sentiment_counts = Counter()
        for article in articles:
            sentiment = article.get('sentiment_analysis', {}).get('overall_sentiment', 'neutral')
            sentiment_counts[sentiment] += 1

        # Identify top topics
        topic_counts = Counter()
        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    topic_counts[topic_info['topic']] += topic_info['confidence']

        top_topics = [topic for topic, _ in topic_counts.most_common(3)]

        # Generate summary
        summary_parts = []
        summary_parts.append(f"Analyzed {len(articles)} articles from multiple sources.")

        if top_topics:
            topics_str = ", ".join(top_topics[:2])
            summary_parts.append(f"Key focus areas: {topics_str}.")

        # Sentiment overview
        if sentiment_counts:
            dominant_sentiment = sentiment_counts.most_common(1)[0][0]
            summary_parts.append(f"Overall sentiment: {dominant_sentiment}.")

        return " ".join(summary_parts)

    def _analyze_daily_trends(self, articles: List[Dict]) -> str:
        """Analyze trending topics for daily briefing."""
        if not articles:
            return "No trending topics identified."

        # Count topic frequencies
        topic_counts = Counter()
        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    topic_counts[topic_info['topic']] += 1

        if not topic_counts:
            return "No specific trends identified in today's news."

        trending_topics = topic_counts.most_common(3)
        trend_descriptions = []

        for topic, count in trending_topics:
            topic_name = topic.replace('_', ' ').title()
            trend_descriptions.append(f"**{topic_name}**: {count} articles")

        return "Today's trending topics:\n" + "\n".join(trend_descriptions)

    def _generate_market_analysis(self, articles: List[Dict]) -> str:
        """Generate market impact analysis."""
        market_articles = []

        for article in articles:
            # Check for market-related content
            if (article.get('smart_categorization', {}).get('industries') or
                article.get('sentiment_analysis', {}).get('market_sentiment')):
                market_articles.append(article)

        if not market_articles:
            return "No significant market impact identified."

        analysis_parts = []
        analysis_parts.append(f"Market-relevant articles: {len(market_articles)}")

        # Analyze sentiment distribution
        market_sentiments = Counter()
        for article in market_articles:
            market_sentiment = article.get('sentiment_analysis', {}).get('market_sentiment', {})
            for sentiment_type in market_sentiment.keys():
                market_sentiments[sentiment_type] += 1

        if market_sentiments:
            dominant_sentiment = market_sentiments.most_common(1)[0][0]
            analysis_parts.append(f"Dominant market sentiment: {dominant_sentiment}")

        return ". ".join(analysis_parts) + "."

    def _generate_recommendations(self, articles: List[Dict], briefing_type: str) -> str:
        """Generate actionable recommendations."""
        recommendations = []

        # Analyze high-impact articles
        high_impact_count = 0
        for article in articles:
            if article.get('smart_categorization', {}).get('events'):
                high_impact_count += 1

        if high_impact_count > 0:
            recommendations.append(f"Monitor {high_impact_count} high-impact events for potential implications")

        # Sentiment-based recommendations
        negative_articles = [a for a in articles
                           if a.get('sentiment_analysis', {}).get('overall_sentiment') == 'negative']

        if len(negative_articles) > len(articles) * 0.3:
            recommendations.append("Consider risk mitigation strategies due to negative sentiment trend")

        # Topic-based recommendations
        topic_counts = Counter()
        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    topic_counts[topic_info['topic']] += 1

        if topic_counts:
            top_topic = topic_counts.most_common(1)[0][0]
            recommendations.append(f"Deep dive into {top_topic.replace('_', ' ')} developments recommended")

        if not recommendations:
            recommendations.append("Continue monitoring current news trends")

        return "• " + "\n• ".join(recommendations)

    def _filter_articles_by_topic(self, topic: str, articles: List[Dict]) -> List[Dict]:
        """Filter articles related to a specific topic."""
        topic_articles = []
        topic_lower = topic.lower().replace('_', ' ')

        for article in articles:
            # Check smart categorization topics
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    if topic_info['topic'].lower().replace('_', ' ') == topic_lower:
                        topic_articles.append(article)
                        break

            # Check title and content
            text = (article.get('title', '') + ' ' +
                   article.get('summary', '') + ' ' +
                   article.get('description', '')).lower()

            if topic_lower in text:
                topic_articles.append(article)

        return topic_articles

    def _organize_articles_by_week(self, articles: List[Dict]) -> Dict[str, List[Dict]]:
        """Organize articles by day of the week."""
        weekly_articles = defaultdict(list)

        # Simple organization by recent days
        for i in range(7):
            day_key = f"day_{i}"
            weekly_articles[day_key] = []

        # Distribute articles across days (simplified)
        for i, article in enumerate(articles):
            day_index = i % 7
            weekly_articles[f"day_{day_index}"].append(article)

        return dict(weekly_articles)

    def _generate_week_summary(self, articles: List[Dict]) -> str:
        """Generate summary for the week."""
        if not articles:
            return "No significant news activity this week."

        # Count articles by sentiment
        sentiment_counts = Counter()
        for article in articles:
            sentiment = article.get('sentiment_analysis', {}).get('overall_sentiment', 'neutral')
            sentiment_counts[sentiment] += 1

        # Count topics
        topic_counts = Counter()
        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    topic_counts[topic_info['topic']] += 1

        summary_parts = []
        summary_parts.append(f"This week saw {len(articles)} significant news developments.")

        if topic_counts:
            top_topic = topic_counts.most_common(1)[0][0]
            summary_parts.append(f"The dominant theme was {top_topic.replace('_', ' ')}.")

        if sentiment_counts:
            dominant_sentiment = sentiment_counts.most_common(1)[0][0]
            summary_parts.append(f"Overall sentiment was {dominant_sentiment}.")

        return " ".join(summary_parts)

    def _analyze_weekly_trends(self, articles: List[Dict]) -> str:
        """Analyze trends over the week."""
        if not articles:
            return "No trends identified this week."

        # Simple trend analysis
        topic_counts = Counter()
        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    topic_counts[topic_info['topic']] += 1

        if not topic_counts:
            return "No specific trends identified in this week's coverage."

        trending_topics = topic_counts.most_common(3)
        trend_descriptions = []

        for topic, count in trending_topics:
            topic_name = topic.replace('_', ' ').title()
            trend_descriptions.append(f"**{topic_name}**: {count} articles")

        return "Weekly trending topics:\n" + "\n".join(trend_descriptions)

    def _generate_comparative_analysis(self, articles: List[Dict]) -> str:
        """Generate comparative analysis for the week."""
        if len(articles) < 2:
            return "Insufficient data for comparative analysis."

        # Compare sentiment distribution
        positive_count = len([a for a in articles
                            if a.get('sentiment_analysis', {}).get('overall_sentiment') == 'positive'])
        negative_count = len([a for a in articles
                            if a.get('sentiment_analysis', {}).get('overall_sentiment') == 'negative'])

        analysis_parts = []

        if positive_count > negative_count:
            analysis_parts.append("Positive sentiment dominated the week")
        elif negative_count > positive_count:
            analysis_parts.append("Negative sentiment was prevalent")
        else:
            analysis_parts.append("Sentiment was balanced")

        # Compare topic diversity
        unique_topics = set()
        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    unique_topics.add(topic_info['topic'])

        analysis_parts.append(f"Coverage spanned {len(unique_topics)} distinct topics")

        return ". ".join(analysis_parts) + "."

    def _generate_strategic_insights(self, articles: List[Dict]) -> str:
        """Generate strategic insights from the week's news."""
        insights = []

        # Analyze high-impact events
        high_impact_count = 0
        for article in articles:
            if article.get('smart_categorization', {}).get('events'):
                high_impact_count += 1

        if high_impact_count > 0:
            insights.append(f"Identified {high_impact_count} high-impact events requiring attention")

        # Analyze market implications
        market_articles = [a for a in articles
                         if a.get('smart_categorization', {}).get('industries')]

        if market_articles:
            insights.append(f"Market implications identified in {len(market_articles)} articles")

        # Analyze emerging themes
        topic_counts = Counter()
        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    topic_counts[topic_info['topic']] += 1

        if topic_counts:
            emerging_topics = [topic for topic, count in topic_counts.items() if count >= 3]
            if emerging_topics:
                insights.append(f"Emerging themes: {', '.join(emerging_topics[:3])}")

        return "• " + "\n• ".join(insights) if insights else "No specific strategic insights identified"

    def _build_daily_briefing_content(self, organized_articles: Dict, executive_summary: str,
                                    trending_analysis: str, market_analysis: str,
                                    recommendations: str) -> str:
        """Build the complete daily briefing content."""
        content_parts = []

        # Header
        date_str = datetime.now().strftime("%B %d, %Y")
        content_parts.append(self.briefing_templates["daily_header"].format(
            date=date_str,
            executive_summary=executive_summary
        ))

        # Topic sections
        for topic, articles in organized_articles.items():
            if len(articles) >= self.config["min_articles_for_trend"]:
                topic_title = topic.replace('_', ' ').title()
                topic_summary = self._generate_topic_summary(articles)
                article_list = self._format_article_list(articles[:3])
                analysis = self._generate_topic_analysis(topic, articles)

                content_parts.append(self.briefing_templates["topic_section"].format(
                    topic_title=topic_title,
                    topic_summary=topic_summary,
                    article_list=article_list,
                    analysis=analysis
                ))

        # Trending section
        if trending_analysis:
            content_parts.append(self.briefing_templates["trend_section"].format(
                trending_analysis=trending_analysis
            ))

        # Market section
        if market_analysis:
            content_parts.append(self.briefing_templates["market_section"].format(
                market_analysis=market_analysis
            ))

        # Recommendations
        if recommendations:
            content_parts.append(self.briefing_templates["recommendation_section"].format(
                recommendations=recommendations
            ))

        # Footer
        total_articles = sum(len(articles) for articles in organized_articles.values())
        source_count = len(set(article.get('source', '') for articles in organized_articles.values()
                             for article in articles))

        content_parts.append(self.briefing_templates["footer"].format(
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M"),
            article_count=total_articles,
            source_count=source_count
        ))

        return "".join(content_parts)

    def _generate_topic_summary(self, articles: List[Dict]) -> str:
        """Generate summary for a topic."""
        if not articles:
            return "No articles available."

        # Use the first article's summary or description
        main_article = articles[0]
        summary = main_article.get('summary', main_article.get('description', ''))

        if summary:
            # Truncate to reasonable length
            return summary[:200] + "..." if len(summary) > 200 else summary

        return f"{len(articles)} articles covering this topic."

    def _format_article_list(self, articles: List[Dict]) -> str:
        """Format list of articles for briefing."""
        article_items = []

        for i, article in enumerate(articles, 1):
            title = article.get('title', 'Untitled')
            source = article.get('source', 'Unknown')
            article_items.append(f"{i}. **{title}** - *{source}*")

        return "\n".join(article_items)

    def _generate_topic_analysis(self, topic: str, articles: List[Dict]) -> str:
        """Generate analysis for a specific topic."""
        analysis_parts = []

        # Sentiment analysis
        sentiments = [article.get('sentiment_analysis', {}).get('overall_sentiment', 'neutral')
                     for article in articles]
        sentiment_counter = Counter(sentiments)

        if sentiment_counter:
            dominant_sentiment = sentiment_counter.most_common(1)[0][0]
            analysis_parts.append(f"Sentiment: {dominant_sentiment}")

        # Event analysis
        events = []
        for article in articles:
            if article.get('smart_categorization', {}).get('events'):
                for event_info in article['smart_categorization']['events']:
                    events.append(event_info['event_type'])

        if events:
            event_counter = Counter(events)
            top_event = event_counter.most_common(1)[0][0]
            analysis_parts.append(f"Key event type: {top_event.replace('_', ' ')}")

        # Coverage analysis
        analysis_parts.append(f"Coverage: {len(articles)} articles")

        return ". ".join(analysis_parts) + "." if analysis_parts else "Standard news coverage."

    def _build_weekly_briefing_content(self, weekly_organization: Dict, week_summary: str,
                                     weekly_trends: str, comparative_analysis: str,
                                     strategic_insights: str) -> str:
        """Build weekly briefing content."""
        content_parts = []

        # Header
        date_range = self._get_week_date_range()
        content_parts.append(self.briefing_templates["weekly_header"].format(
            date_range=date_range,
            week_summary=week_summary
        ))

        # Weekly trends
        if weekly_trends:
            content_parts.append(self.briefing_templates["trend_section"].format(
                trending_analysis=weekly_trends
            ))

        # Comparative analysis
        if comparative_analysis:
            content_parts.append(f"## Comparative Analysis\n{comparative_analysis}\n\n")

        # Strategic insights
        if strategic_insights:
            content_parts.append(f"## Strategic Insights\n{strategic_insights}\n\n")

        # Footer
        total_articles = sum(len(articles) for articles in weekly_organization.values())
        content_parts.append(self.briefing_templates["footer"].format(
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M"),
            article_count=total_articles,
            source_count=5  # Placeholder
        ))

        return "".join(content_parts)

    def _get_week_date_range(self) -> str:
        """Get the current week's date range."""
        from datetime import timedelta

        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6)

        return f"{start_of_week.strftime('%B %d')} - {end_of_week.strftime('%B %d, %Y')}"

    def _generate_topic_overview(self, topic: str, articles: List[Dict]) -> str:
        """Generate overview for a specific topic."""
        if not articles:
            return f"No recent coverage found for {topic.replace('_', ' ')}."

        overview_parts = []
        overview_parts.append(f"Analysis of {len(articles)} articles covering {topic.replace('_', ' ')}.")

        # Sentiment overview
        sentiments = [article.get('sentiment_analysis', {}).get('overall_sentiment', 'neutral')
                     for article in articles]
        sentiment_counter = Counter(sentiments)

        if sentiment_counter:
            dominant_sentiment = sentiment_counter.most_common(1)[0][0]
            overview_parts.append(f"Overall sentiment: {dominant_sentiment}.")

        # Source diversity
        sources = [article.get('source', 'Unknown') for article in articles]
        unique_sources = len(set(sources))
        overview_parts.append(f"Coverage from {unique_sources} different sources.")

        return " ".join(overview_parts)

    def _analyze_topic_evolution(self, topic: str, articles: List[Dict]) -> str:
        """Analyze how a topic has evolved over time."""
        if len(articles) < 2:
            return "Insufficient data to analyze topic evolution."

        # Simple evolution analysis
        evolution_parts = []

        # Analyze sentiment changes
        positive_count = len([a for a in articles
                            if a.get('sentiment_analysis', {}).get('overall_sentiment') == 'positive'])
        negative_count = len([a for a in articles
                            if a.get('sentiment_analysis', {}).get('overall_sentiment') == 'negative'])

        if positive_count > negative_count:
            evolution_parts.append("Sentiment has been generally positive")
        elif negative_count > positive_count:
            evolution_parts.append("Sentiment has been predominantly negative")
        else:
            evolution_parts.append("Sentiment has been mixed")

        # Analyze coverage intensity
        if len(articles) >= 5:
            evolution_parts.append("with high media attention")
        elif len(articles) >= 3:
            evolution_parts.append("with moderate coverage")
        else:
            evolution_parts.append("with limited coverage")

        return ". ".join(evolution_parts) + "."

    def _identify_topic_key_players(self, topic: str, articles: List[Dict]) -> List[str]:
        """Identify key players mentioned in topic coverage."""
        key_players = []

        # Extract entities from articles
        for article in articles:
            # Simple entity extraction from titles
            title = article.get('title', '')

            # Look for company names
            import re
            companies = re.findall(r'\b[A-Z][a-z]+ (?:Inc|Corp|Ltd|LLC|Co)\b', title)
            key_players.extend(companies)

            # Look for well-known companies
            known_companies = ['Apple', 'Google', 'Microsoft', 'Amazon', 'Tesla', 'Meta', 'Netflix']
            for company in known_companies:
                if company in title:
                    key_players.append(company)

        # Remove duplicates and return top players
        unique_players = list(set(key_players))
        return unique_players[:5]

    def _analyze_topic_sentiment_trends(self, topic: str, articles: List[Dict]) -> Dict[str, Any]:
        """Analyze sentiment trends for a topic."""
        sentiment_data = {
            "overall_sentiment": "neutral",
            "sentiment_distribution": {},
            "trend_direction": "stable"
        }

        # Count sentiments
        sentiments = [article.get('sentiment_analysis', {}).get('overall_sentiment', 'neutral')
                     for article in articles]
        sentiment_counter = Counter(sentiments)

        sentiment_data["sentiment_distribution"] = dict(sentiment_counter)

        if sentiment_counter:
            dominant_sentiment = sentiment_counter.most_common(1)[0][0]
            sentiment_data["overall_sentiment"] = dominant_sentiment

        # Simple trend analysis
        if sentiment_counter.get('positive', 0) > sentiment_counter.get('negative', 0):
            sentiment_data["trend_direction"] = "improving"
        elif sentiment_counter.get('negative', 0) > sentiment_counter.get('positive', 0):
            sentiment_data["trend_direction"] = "declining"

        return sentiment_data

    def _generate_topic_future_outlook(self, topic: str, articles: List[Dict]) -> str:
        """Generate future outlook for a topic."""
        outlook_parts = []

        # Analyze recent events
        recent_events = []
        for article in articles:
            if article.get('smart_categorization', {}).get('events'):
                for event_info in article['smart_categorization']['events']:
                    recent_events.append(event_info['event_type'])

        if recent_events:
            event_counter = Counter(recent_events)
            top_event = event_counter.most_common(1)[0][0]
            outlook_parts.append(f"Recent {top_event.replace('_', ' ')} activity suggests continued development")

        # Analyze sentiment for outlook
        positive_count = len([a for a in articles
                            if a.get('sentiment_analysis', {}).get('overall_sentiment') == 'positive'])
        total_articles = len(articles)

        if positive_count > total_articles * 0.6:
            outlook_parts.append("with positive momentum expected")
        elif positive_count < total_articles * 0.3:
            outlook_parts.append("with challenges ahead")
        else:
            outlook_parts.append("with mixed prospects")

        return ". ".join(outlook_parts) + "." if outlook_parts else "Outlook remains uncertain based on current coverage."

    def _build_topic_deep_dive_content(self, topic: str, overview: str, evolution: str,
                                     key_players: List[str], sentiment_trends: Dict,
                                     future_outlook: str, articles: List[Dict]) -> str:
        """Build topic deep dive content."""
        content_parts = []

        # Header
        topic_title = topic.replace('_', ' ').title()
        content_parts.append(f"# Deep Dive Analysis: {topic_title}\n\n")

        # Overview
        content_parts.append(f"## Overview\n{overview}\n\n")

        # Evolution
        content_parts.append(f"## Topic Evolution\n{evolution}\n\n")

        # Key Players
        if key_players:
            content_parts.append(f"## Key Players\n")
            for player in key_players:
                content_parts.append(f"• {player}\n")
            content_parts.append("\n")

        # Sentiment Analysis
        content_parts.append(f"## Sentiment Analysis\n")
        content_parts.append(f"Overall Sentiment: {sentiment_trends.get('overall_sentiment', 'neutral')}\n")
        content_parts.append(f"Trend Direction: {sentiment_trends.get('trend_direction', 'stable')}\n\n")

        # Future Outlook
        content_parts.append(f"## Future Outlook\n{future_outlook}\n\n")

        # Recent Articles
        content_parts.append(f"## Recent Coverage\n")
        for i, article in enumerate(articles[:5], 1):
            title = article.get('title', 'Untitled')
            source = article.get('source', 'Unknown')
            content_parts.append(f"{i}. **{title}** - *{source}*\n")

        # Footer
        content_parts.append(f"\n---\n*Analysis based on {len(articles)} articles*\n")

        return "".join(content_parts)

    # Additional helper methods for comprehensive briefing generation

    def _calculate_sentiment_distribution(self, articles: List[Dict]) -> Dict[str, float]:
        """Calculate sentiment distribution for articles."""
        sentiments = [article.get('sentiment_analysis', {}).get('overall_sentiment', 'neutral')
                     for article in articles]
        sentiment_counter = Counter(sentiments)
        total = len(articles)

        return {sentiment: count/total for sentiment, count in sentiment_counter.items()} if total > 0 else {}

    def _extract_topic_themes(self, articles: List[Dict]) -> List[str]:
        """Extract main themes from topic articles."""
        themes = []

        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    themes.append(topic_info['topic'])

        theme_counter = Counter(themes)
        return [theme for theme, count in theme_counter.most_common(5)]

    def _assess_topic_market_impact(self, articles: List[Dict]) -> str:
        """Assess market impact for topic articles."""
        market_articles = [a for a in articles
                         if a.get('smart_categorization', {}).get('industries')]

        if not market_articles:
            return "low"

        impact_ratio = len(market_articles) / len(articles)

        if impact_ratio >= 0.7:
            return "high"
        elif impact_ratio >= 0.4:
            return "medium"
        else:
            return "low"

    def _assess_topic_trend_direction(self, articles: List[Dict]) -> str:
        """Assess trend direction for topic."""
        positive_count = len([a for a in articles
                            if a.get('sentiment_analysis', {}).get('overall_sentiment') == 'positive'])
        negative_count = len([a for a in articles
                            if a.get('sentiment_analysis', {}).get('overall_sentiment') == 'negative'])

        if positive_count > negative_count * 1.5:
            return "rising"
        elif negative_count > positive_count * 1.5:
            return "declining"
        else:
            return "stable"

    def _generate_comparison_insights(self, topic_analyses: Dict[str, Dict]) -> List[str]:
        """Generate insights from topic comparison."""
        insights = []

        if len(topic_analyses) < 2:
            return ["Insufficient topics for comparison"]

        # Compare article counts
        topic_counts = {topic: analysis['article_count'] for topic, analysis in topic_analyses.items()}
        most_covered = max(topic_counts, key=topic_counts.get)
        least_covered = min(topic_counts, key=topic_counts.get)

        insights.append(f"Most covered topic: {most_covered.replace('_', ' ')} ({topic_counts[most_covered]} articles)")
        insights.append(f"Least covered topic: {least_covered.replace('_', ' ')} ({topic_counts[least_covered]} articles)")

        return insights

    def _build_comparative_analysis_content(self, topics: List[str],
                                          topic_analyses: Dict[str, Dict],
                                          comparison_insights: List[str]) -> str:
        """Build comparative analysis content."""
        content_parts = []

        # Header
        content_parts.append(f"# Comparative Analysis: {', '.join(topics)}\n\n")

        # Topic summaries
        content_parts.append("## Topic Overview\n")
        for topic in topics:
            if topic in topic_analyses:
                analysis = topic_analyses[topic]
                topic_name = topic.replace('_', ' ').title()
                content_parts.append(f"### {topic_name}\n")
                content_parts.append(f"- Articles: {analysis['article_count']}\n")
                content_parts.append(f"- Market Impact: {analysis.get('market_impact', 'unknown')}\n")
                content_parts.append(f"- Trend Direction: {analysis.get('trend_direction', 'unknown')}\n\n")

        # Comparison insights
        content_parts.append("## Key Insights\n")
        for insight in comparison_insights:
            content_parts.append(f"• {insight}\n")

        content_parts.append(f"\n---\n*Analysis generated on {datetime.now().strftime('%Y-%m-%d %H:%M')}*\n")

        return "".join(content_parts)

    def _identify_high_impact_articles(self, articles: List[Dict]) -> List[Dict]:
        """Identify high-impact articles for executive summary."""
        high_impact = []

        for article in articles:
            impact_score = 0

            # Check for events
            if article.get('smart_categorization', {}).get('events'):
                impact_score += 2

            # Check for industries
            if article.get('smart_categorization', {}).get('industries'):
                impact_score += 1

            # Check for strong sentiment
            sentiment_scores = article.get('sentiment_analysis', {}).get('sentiment_scores', {})
            compound = abs(sentiment_scores.get('compound', 0))
            if compound > 0.5:
                impact_score += 1

            if impact_score >= 2:
                high_impact.append(article)

        return high_impact

    def _generate_key_insights(self, articles: List[Dict], focus_areas: Optional[List[str]]) -> List[str]:
        """Generate key insights for executive summary."""
        insights = []

        # Sentiment insights
        sentiment_counts = Counter()
        for article in articles:
            sentiment = article.get('sentiment_analysis', {}).get('overall_sentiment', 'neutral')
            sentiment_counts[sentiment] += 1

        if sentiment_counts:
            dominant_sentiment = sentiment_counts.most_common(1)[0][0]
            insights.append(f"Overall sentiment is {dominant_sentiment} across {len(articles)} articles")

        # Topic insights
        topic_counts = Counter()
        for article in articles:
            if article.get('smart_categorization', {}).get('topics'):
                for topic_info in article['smart_categorization']['topics']:
                    topic_counts[topic_info['topic']] += 1

        if topic_counts:
            top_topics = [topic.replace('_', ' ') for topic, _ in topic_counts.most_common(3)]
            insights.append(f"Key focus areas: {', '.join(top_topics)}")

        return insights

    def _assess_strategic_implications(self, articles: List[Dict]) -> List[str]:
        """Assess strategic implications from articles."""
        implications = []

        # Market implications
        market_articles = [a for a in articles
                         if a.get('smart_categorization', {}).get('industries')]

        if market_articles:
            implications.append(f"Market implications identified in {len(market_articles)} articles")

        return implications

    def _generate_action_items(self, articles: List[Dict]) -> List[str]:
        """Generate actionable items from articles."""
        action_items = []

        # High-impact events
        high_impact_events = 0
        for article in articles:
            events = article.get('smart_categorization', {}).get('events', [])
            high_impact_events += len(events)

        if high_impact_events > 0:
            action_items.append(f"Monitor {high_impact_events} significant events")

        return action_items

    def _create_risk_assessment(self, articles: List[Dict]) -> Dict[str, Any]:
        """Create risk assessment from articles."""
        risk_assessment = {
            "overall_risk_level": "low",
            "risk_factors": [],
            "risk_score": 0.0
        }

        risk_score = 0.0
        risk_factors = []

        # Negative sentiment risk
        negative_count = len([a for a in articles
                            if a.get('sentiment_analysis', {}).get('overall_sentiment') == 'negative'])

        if negative_count > len(articles) * 0.4:
            risk_score += 0.3
            risk_factors.append("High negative sentiment in news coverage")

        # Determine overall risk level
        if risk_score >= 0.6:
            risk_level = "high"
        elif risk_score >= 0.3:
            risk_level = "medium"
        else:
            risk_level = "low"

        risk_assessment.update({
            "overall_risk_level": risk_level,
            "risk_factors": risk_factors,
            "risk_score": risk_score
        })

        return risk_assessment

    def _calculate_overall_risk_level(self, risk_assessment: Dict[str, Any]) -> str:
        """Calculate overall risk level."""
        return risk_assessment.get("overall_risk_level", "low")

    def _build_executive_summary_content(self, key_insights: List[str],
                                       strategic_implications: List[str],
                                       action_items: List[str],
                                       risk_assessment: Dict[str, Any]) -> str:
        """Build executive summary content."""
        content_parts = []

        # Header
        content_parts.append(f"# Executive Summary - {datetime.now().strftime('%B %d, %Y')}\n\n")

        # Key Insights
        content_parts.append("## Key Insights\n")
        for insight in key_insights:
            content_parts.append(f"• {insight}\n")
        content_parts.append("\n")

        # Strategic Implications
        if strategic_implications:
            content_parts.append("## Strategic Implications\n")
            for implication in strategic_implications:
                content_parts.append(f"• {implication}\n")
            content_parts.append("\n")

        # Action Items
        if action_items:
            content_parts.append("## Recommended Actions\n")
            for action in action_items:
                content_parts.append(f"• {action}\n")
            content_parts.append("\n")

        # Risk Assessment
        content_parts.append("## Risk Assessment\n")
        content_parts.append(f"**Overall Risk Level:** {risk_assessment['overall_risk_level'].upper()}\n\n")

        content_parts.append(f"\n---\n*Executive Summary generated on {datetime.now().strftime('%Y-%m-%d %H:%M')}*\n")

        return "".join(content_parts)
