/* News Feed Application - Modern Design System */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap');

/* CSS Custom Properties (Design Tokens) */
:root {
  /* Colors - Light Theme */
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-primary-light: #dbeafe;
  --color-secondary: #6366f1;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-error: #ef4444;
  --color-info: #06b6d4;
  --color-info-light: #e0f7fa;
  --color-info-dark: #0891b2;
  
  /* Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* Background Colors */
  --bg-primary: var(--color-white);
  --bg-secondary: var(--color-gray-50);
  --bg-tertiary: var(--color-gray-100);
  --bg-sidebar: var(--color-gray-900);
  --bg-card: var(--color-white);
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* Text Colors */
  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-600);
  --text-tertiary: var(--color-gray-500);
  --text-inverse: var(--color-white);
  --text-muted: var(--color-gray-400);
  
  /* Border Colors */
  --border-primary: var(--color-gray-200);
  --border-secondary: var(--color-gray-300);
  --border-focus: var(--color-primary);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: var(--color-gray-900);
  --bg-secondary: var(--color-gray-800);
  --bg-tertiary: var(--color-gray-700);
  --bg-sidebar: var(--color-gray-900);
  --bg-card: var(--color-gray-800);
  
  --text-primary: var(--color-gray-100);
  --text-secondary: var(--color-gray-300);
  --text-tertiary: var(--color-gray-400);
  --text-muted: var(--color-gray-500);
  
  --border-primary: var(--color-gray-700);
  --border-secondary: var(--color-gray-600);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: var(--leading-tight);
  margin: 0 0 var(--space-4) 0;
  color: var(--text-primary);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin: 0 0 var(--space-4) 0;
  color: var(--text-secondary);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

/* Layout Components */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }

/* Grid System */
.grid {
  display: grid;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* Spacing Utilities */
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }

.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }

.m-2 { margin: var(--space-2); }
.m-4 { margin: var(--space-4); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }

/* Component Styles */
.card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
  transition: box-shadow var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  font-weight: 500;
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-200);
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
}

/* Form Elements */
.form-input {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  transition: border-color var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  cursor: pointer;
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

/* Status Messages */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-4);
  border-left: 4px solid;
}

.alert-info {
  background-color: rgba(6, 182, 212, 0.1);
  border-left-color: var(--color-info);
  color: var(--color-info);
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-left-color: var(--color-success);
  color: var(--color-success);
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-left-color: var(--color-warning);
  color: var(--color-warning);
}

.alert-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-left-color: var(--color-error);
  color: var(--color-error);
}

/* Loading Spinner */
.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-gray-300);
  border-top: 2px solid var(--color-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  h1 { font-size: var(--text-3xl); }
  h2 { font-size: var(--text-2xl); }
  h3 { font-size: var(--text-xl); }
}

/* Utility Classes */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.text-sm { font-size: var(--text-sm); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.border { border: 1px solid var(--border-primary); }
.border-t { border-top: 1px solid var(--border-primary); }
.border-b { border-bottom: 1px solid var(--border-primary); }

/* Interactive Components */

/* Status Display */
.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-3);
}

.status-content #statusText {
  flex: 1;
}

.status-content #statusResetBtn {
  flex-shrink: 0;
  font-size: var(--text-sm);
  padding: var(--space-1) var(--space-2);
}

/* Category Badges */
.category-badge {
  display: inline-block;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.category-badge.business { background-color: #3b82f6; color: white; }
.category-badge.market { background-color: #10b981; color: white; }
.category-badge.technology { background-color: #8b5cf6; color: white; }
.category-badge.cryptocurrency { background-color: #f59e0b; color: white; }
.category-badge.health { background-color: #ef4444; color: white; }
.category-badge.sports { background-color: #06b6d4; color: white; }
.category-badge.science { background-color: #84cc16; color: white; }
.category-badge.politics { background-color: #dc2626; color: white; }
.category-badge.entertainment { background-color: #ec4899; color: white; }
.category-badge.education { background-color: #6366f1; color: white; }
.category-badge.environment { background-color: #059669; color: white; }
.category-badge.automotive { background-color: #374151; color: white; }
.category-badge.real-estate { background-color: #92400e; color: white; }
.category-badge.food-beverage { background-color: #ea580c; color: white; }
.category-badge.travel-tourism { background-color: #0891b2; color: white; }
.category-badge.energy { background-color: #facc15; color: #1f2937; }
.category-badge.world-news { background-color: #7c3aed; color: white; }
.category-badge.general { background-color: var(--color-gray-500); color: white; }

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-tooltip);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  max-width: 400px;
}

.toast {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);
  animation: slideInRight 0.3s ease-out;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
}

.toast-icon {
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: var(--text-sm);
  color: var(--text-primary);
}

.toast-close {
  background: none;
  border: none;
  font-size: var(--text-lg);
  cursor: pointer;
  color: var(--text-tertiary);
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.toast-close:hover {
  background-color: var(--bg-tertiary);
}

.toast-success { border-left: 4px solid var(--color-success); }
.toast-error { border-left: 4px solid var(--color-error); }
.toast-warning { border-left: 4px solid var(--color-warning); }
.toast-info { border-left: 4px solid var(--color-info); }

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.loading-content {
  background-color: var(--bg-card);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-xl);
}

.loading-content .spinner {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-4) auto;
}

.loading-text {
  font-size: var(--text-lg);
  font-weight: 500;
  color: var(--text-primary);
}

/* Skeleton Loading */
.skeleton-item {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-line {
  height: 1rem;
  background-color: var(--color-gray-300);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-2);
}

.skeleton-title {
  height: 1.5rem;
  width: 75%;
}

.skeleton-meta {
  height: 0.875rem;
  width: 50%;
}

.skeleton-text {
  width: 100%;
}

.skeleton-text-short {
  width: 60%;
}

[data-theme="dark"] .skeleton-line {
  background-color: var(--color-gray-600);
}

/* News Components */
.category-section {
  margin-bottom: var(--space-8);
}

.category-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-2);
  border-bottom: 2px solid var(--color-primary);
  display: inline-block;
}

.news-grid {
  display: grid;
  gap: var(--space-6);
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.news-item {
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.news-item:hover {
  transform: translateY(-2px);
}

.news-header {
  position: relative;
}

.news-title {
  color: var(--text-primary);
  line-height: var(--leading-tight);
  margin-bottom: var(--space-2);
}

.news-title:hover {
  color: var(--color-primary);
}

.news-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  align-items: center;
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

.source-badge {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
}

.time-badge {
  color: var(--text-muted);
}

.sentiment-badge {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
}

.sentiment-positive {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
}

.sentiment-negative {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--color-error);
}

.sentiment-neutral {
  background-color: rgba(107, 114, 128, 0.1);
  color: var(--color-gray-600);
}

.read-more-link {
  color: var(--color-primary);
  font-weight: 500;
  text-decoration: none;
  transition: color var(--transition-fast);
}

.read-more-link:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

.news-summary {
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-4);
}

.news-summary p {
  margin: 0;
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* Empty and Error States */
.empty-state,
.error-state {
  padding: var(--space-12) var(--space-4);
  text-align: center;
}

.empty-icon,
.error-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
  opacity: 0.6;
}

.empty-state h3,
.error-state h3 {
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.empty-state p,
.error-state p {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Enhanced Responsive Design */

/* Mobile-first breakpoints */
@media (max-width: 480px) {
  /* Extra small devices */
  .container {
    padding: 0 var(--space-2);
  }

  .toast-container {
    left: var(--space-2);
    right: var(--space-2);
    top: var(--space-2);
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .news-item {
    padding: var(--space-4);
  }

  .news-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .category-title {
    font-size: var(--text-lg);
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .control-actions {
    flex-direction: column;
    gap: var(--space-2);
  }

  .pagination-controls {
    flex-direction: column;
    gap: var(--space-2);
  }

  .pagination-controls button {
    width: 100%;
  }
}

@media (max-width: 768px) {
  /* Tablet and small desktop */
  .toast-container {
    left: var(--space-4);
    right: var(--space-4);
    max-width: none;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .news-meta {
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .category-title {
    font-size: var(--text-xl);
  }

  .filter-controls {
    flex-direction: column;
    gap: var(--space-3);
  }

  .search-input {
    max-width: 100%;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* Tablet landscape */
  .news-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }

  .container {
    max-width: 900px;
  }
}

@media (min-width: 1025px) {
  /* Desktop */
  .news-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  /* Touch devices */
  .btn {
    min-height: 44px; /* iOS recommended touch target */
    padding: var(--space-3) var(--space-4);
  }

  .btn-sm {
    min-height: 36px;
    padding: var(--space-2) var(--space-3);
  }

  .form-input,
  .form-select {
    min-height: 44px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .toast-close {
    min-width: 44px;
    min-height: 44px;
  }

  .theme-toggle {
    min-width: 44px;
    min-height: 44px;
  }

  /* Larger tap targets for source management */
  .source-item button {
    min-width: 36px;
    min-height: 36px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .spinner {
    border-width: 1px;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .spinner {
    animation: none;
    border-top-color: var(--color-primary);
  }
}

/* Dark mode media query support */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    --bg-primary: var(--color-gray-900);
    --bg-secondary: var(--color-gray-800);
    --bg-tertiary: var(--color-gray-700);
    --bg-sidebar: var(--color-gray-900);
    --bg-card: var(--color-gray-800);

    --text-primary: var(--color-gray-100);
    --text-secondary: var(--color-gray-300);
    --text-tertiary: var(--color-gray-400);
    --text-muted: var(--color-gray-500);

    --border-primary: var(--color-gray-700);
    --border-secondary: var(--color-gray-600);
  }
}

/* Advanced Search Components */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.search-basic {
  display: flex;
  gap: var(--space-3);
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.search-input-container {
  position: relative;
  flex: 1;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-top: none;
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-dropdown);
  max-height: 200px;
  overflow-y: auto;
}

.search-suggestion-item {
  padding: var(--space-2) var(--space-3);
  cursor: pointer;
  border-bottom: 1px solid var(--border-primary);
  transition: background-color var(--transition-fast);
}

.search-suggestion-item:hover,
.search-suggestion-item.active {
  background-color: var(--bg-tertiary);
}

.search-suggestion-item:last-child {
  border-bottom: none;
}

.search-actions {
  display: flex;
  gap: var(--space-2);
}

.advanced-search-panel {
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-4);
  border: 1px solid var(--border-primary);
}

.search-filters {
  margin-bottom: var(--space-4);
}

.filter-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-3);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.filter-group .form-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.saved-searches-section {
  border-top: 1px solid var(--border-primary);
  padding-top: var(--space-4);
}

.saved-searches-list {
  max-height: 150px;
  overflow-y: auto;
  margin-bottom: var(--space-3);
}

.saved-search-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2);
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-2);
  border: 1px solid var(--border-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.saved-search-item:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-1px);
}

.saved-search-info {
  flex: 1;
}

.saved-search-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.saved-search-meta {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: var(--space-1);
}

.saved-search-actions {
  display: flex;
  gap: var(--space-2);
}

.search-result-item {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  border: 1px solid var(--border-primary);
  transition: all var(--transition-normal);
}

.search-result-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.search-result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-3);
}

.search-result-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  line-height: var(--leading-tight);
  margin: 0;
}

.search-result-score {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
}

.search-result-meta {
  display: flex;
  gap: var(--space-4);
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-bottom: var(--space-3);
}

.search-result-content {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.search-result-content mark {
  background-color: var(--color-warning);
  color: var(--color-gray-900);
  padding: 0 var(--space-1);
  border-radius: var(--radius-sm);
}

.search-result-matched-fields {
  margin-top: var(--space-3);
  padding-top: var(--space-3);
  border-top: 1px solid var(--border-primary);
}

.matched-field-tag {
  display: inline-block;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  margin-right: var(--space-2);
  margin-bottom: var(--space-1);
}

/* LLM Provider Components */
.llm-provider-card {
  background-color: var(--color-gray-700);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  margin-bottom: var(--space-2);
  border: 1px solid var(--color-gray-600);
}

.llm-provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.llm-provider-name {
  font-weight: 500;
  color: var(--text-inverse);
  font-size: var(--text-sm);
}

.llm-provider-status {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-xs);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
}

.status-indicator.online {
  background-color: var(--color-success);
}

.status-indicator.offline {
  background-color: var(--color-error);
}

.status-indicator.warning {
  background-color: var(--color-warning);
}

.llm-provider-stats {
  display: flex;
  justify-content: space-between;
  font-size: var(--text-xs);
  color: var(--color-gray-400);
  margin-top: var(--space-2);
}

.llm-provider-actions {
  display: flex;
  gap: var(--space-1);
  margin-top: var(--space-2);
}

.llm-provider-actions .btn {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
}

.budget-bar {
  width: 100%;
  height: 4px;
  background-color: var(--color-gray-600);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.budget-used {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success) 0%, var(--color-warning) 70%, var(--color-error) 100%);
  transition: width var(--transition-normal);
}

.cost-display {
  font-family: var(--font-family-mono);
  font-size: var(--text-xs);
  color: var(--color-gray-300);
}

/* Insights Dashboard */
.insights-dashboard {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.insights-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-4);
}

.insight-card {
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  border: 1px solid var(--border-primary);
  transition: all var(--transition-normal);
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.insight-card h4 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

/* Trending Topics */
.trending-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.trending-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2);
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  transition: background-color var(--transition-fast);
}

.trending-item:hover {
  background-color: var(--bg-secondary);
}

.trending-keyword {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.trending-score {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
}

/* Sentiment Chart */
.sentiment-chart {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.sentiment-bar {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.sentiment-label {
  min-width: 60px;
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.sentiment-progress {
  flex: 1;
  height: 8px;
  background-color: var(--color-gray-300);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.sentiment-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

.sentiment-fill.positive {
  background-color: var(--color-success);
}

.sentiment-fill.negative {
  background-color: var(--color-error);
}

.sentiment-fill.neutral {
  background-color: var(--color-gray-500);
}

.sentiment-percentage {
  min-width: 40px;
  text-align: right;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* Content Stats */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
}

.stat-item {
  text-align: center;
  padding: var(--space-3);
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.stat-value {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Sources List */
.sources-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  max-height: 200px;
  overflow-y: auto;
}

.source-item-insight {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2);
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.source-name {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: 500;
}

.source-count {
  background-color: var(--color-secondary-light);
  color: var(--color-secondary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
}

/* ===== AI FEATURES DASHBOARD ===== */
.ai-features-dashboard {
  background-color: var(--bg-card);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
  margin-bottom: var(--space-6);
}

.ai-features-header {
  margin-bottom: var(--space-6);
}

.ai-features-header h3 {
  margin: 0 0 var(--space-4) 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.ai-features-tabs {
  display: flex;
  gap: var(--space-2);
  border-bottom: 2px solid var(--border-primary);
  margin-bottom: var(--space-6);
  overflow-x: auto;
  padding-bottom: var(--space-2);
}

.ai-tab-btn {
  background: none;
  border: none;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
}

.ai-tab-btn:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.ai-tab-btn.active {
  background-color: var(--color-primary);
  color: white;
  font-weight: 600;
}

.ai-tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-primary);
}

.ai-tab-content {
  display: none;
  animation: fadeIn 0.3s ease;
}

.ai-tab-content.active {
  display: block;
}

/* AI Chat Assistant Styles */
.ai-chat-container {
  max-width: 100%;
}

.ai-chat-header {
  margin-bottom: var(--space-4);
}

.ai-chat-header h4 {
  margin: 0 0 var(--space-2) 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.ai-chat-messages {
  height: 400px;
  overflow-y: auto;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  background-color: var(--bg-secondary);
}

.ai-message {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  animation: slideInUp 0.3s ease;
}

.ai-message.user-message {
  flex-direction: row-reverse;
}

.ai-message.user-message .message-content {
  background-color: var(--color-primary);
  color: white;
  margin-left: var(--space-6);
  margin-right: 0;
}

.ai-message.system-message .message-content {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  flex-shrink: 0;
}

.message-content {
  background-color: var(--bg-card);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  margin-right: var(--space-6);
  flex: 1;
}

.message-content p {
  margin: 0 0 var(--space-2) 0;
}

.message-content p:last-child {
  margin-bottom: 0;
}

.message-content ul {
  margin: var(--space-2) 0;
  padding-left: var(--space-4);
}

.message-content li {
  margin-bottom: var(--space-1);
}

.ai-chat-input-container {
  border-top: 1px solid var(--border-primary);
  padding-top: var(--space-4);
}

.ai-chat-input-wrapper {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.ai-chat-input {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  font-size: 0.95rem;
}

.ai-chat-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.ai-chat-send-btn {
  padding: var(--space-3) var(--space-4);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-chat-send-btn:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
}

.ai-chat-send-btn:disabled {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.ai-chat-suggestions {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.suggestion-btn {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  color: var(--text-secondary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-btn:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

/* AI Features Tab Content Styles */
.smart-categorization-container,
.recommendations-container,
.briefing-container,
.relationships-container {
  padding: var(--space-4);
}

.categorization-header,
.recommendations-header,
.briefing-header,
.relationships-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
}

.categorization-results,
.recommendations-results,
.briefing-results,
.relationships-results {
  min-height: 200px;
  max-height: 500px;
  overflow-y: auto;
}

/* Categorization Styles */
.categorization-item {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  margin-bottom: var(--space-3);
}

.categorization-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.categorization-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.category-tag {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.topic-tag {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
}

.industry-tag {
  background-color: var(--color-secondary-light);
  color: var(--color-secondary);
}

.neutral-tag {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-secondary);
}

/* Recommendations Styles */
.recommendation-item {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  margin-bottom: var(--space-3);
  transition: all 0.2s ease;
}

.recommendation-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.recommendation-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.recommendation-reason {
  color: var(--text-secondary);
  margin: 0 0 var(--space-2) 0;
  font-size: 0.9rem;
}

.recommendation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.recommendation-score {
  background-color: var(--color-success-light);
  color: var(--color-success);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-weight: 500;
}

/* Briefing Styles */
.briefing-controls {
  display: flex;
  gap: var(--space-2);
}

.briefing-input-section {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.briefing-content {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.briefing-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
}

.briefing-section {
  margin-bottom: var(--space-4);
}

.briefing-section h5 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.briefing-text {
  line-height: 1.6;
  color: var(--text-primary);
}

.briefing-meta {
  margin-top: var(--space-4);
  padding-top: var(--space-2);
  border-top: 1px solid var(--border-primary);
}

/* Briefing Markdown Styles */
.briefing-markdown {
  line-height: 1.7;
  color: var(--text-primary);
}

.briefing-markdown .briefing-h1 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: var(--space-6) 0 var(--space-4) 0;
  padding-bottom: var(--space-3);
  border-bottom: 2px solid var(--color-primary);
}

.briefing-markdown .briefing-h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: var(--space-5) 0 var(--space-3) 0;
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
}

.briefing-markdown .briefing-h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: var(--space-4) 0 var(--space-2) 0;
}

.briefing-markdown .briefing-paragraph {
  margin-bottom: var(--space-3);
  line-height: 1.7;
  color: var(--text-primary);
}

.briefing-markdown .briefing-list {
  margin: var(--space-3) 0;
  padding-left: var(--space-5);
}

.briefing-markdown .briefing-bullet {
  margin-bottom: var(--space-2);
  line-height: 1.6;
  color: var(--text-primary);
}

.briefing-markdown .briefing-numbered {
  margin-bottom: var(--space-2);
  line-height: 1.6;
  color: var(--text-primary);
}

.briefing-markdown strong {
  font-weight: 600;
  color: var(--text-primary);
}

.briefing-markdown em {
  font-style: italic;
  color: var(--text-secondary);
}

.briefing-metadata {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  margin-top: var(--space-4);
}

.metadata-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
}

.metadata-item {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  padding: var(--space-1) var(--space-2);
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

/* Relationships Styles */
.relationship-section {
  margin-bottom: var(--space-6);
}

.relationship-section h5 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
}

.relationship-group {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  margin-bottom: var(--space-3);
}

.relationship-articles {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.relationship-article {
  font-size: 0.9rem;
  color: var(--text-secondary);
  padding: var(--space-1) 0;
}

.relationship-original,
.relationship-followup {
  font-size: 0.9rem;
  margin-bottom: var(--space-1);
}

.relationship-original {
  color: var(--text-primary);
  font-weight: 500;
}

.relationship-followup {
  color: var(--text-secondary);
}

.relationship-score {
  font-size: 0.8rem;
  color: var(--color-primary);
  font-weight: 500;
  margin-top: var(--space-2);
}

/* Article Action Buttons */
.news-actions {
  display: flex;
  gap: var(--space-2);
  padding-top: var(--space-3);
  border-top: 1px solid var(--border-primary);
}

.explain-article-btn,
.categorize-article-btn {
  font-size: 0.8rem;
  padding: var(--space-2) var(--space-3);
}

.explain-article-btn:hover {
  background-color: var(--color-primary);
  color: white;
}

.categorize-article-btn:hover {
  background-color: var(--color-secondary);
  color: white;
}

/* Article Explanation Styles */
.article-explanation {
  margin-top: var(--space-4);
  padding: var(--space-4);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  animation: slideDown 0.3s ease;
}

.article-explanation-content h5 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
}

.explanation-text {
  line-height: 1.6;
  color: var(--text-primary);
}

.explanation-text p {
  margin-bottom: var(--space-2);
}

.explanation-text p:last-child {
  margin-bottom: 0;
}

/* Enhanced explanation components */
.explanation-key-info {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  margin: var(--space-3) 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.explanation-follow-ups {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  margin-top: var(--space-3);
}

.explanation-follow-ups h6 {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.explanation-follow-ups ul {
  margin: 0;
  padding-left: var(--space-4);
  list-style-type: disc;
}

.explanation-follow-ups li {
  margin-bottom: var(--space-1);
  font-size: 0.85rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Follow-up question buttons */
.follow-up-questions {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.follow-up-question-btn {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  line-height: 1.4;
}

.follow-up-question-btn:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.follow-up-question-btn:active {
  transform: translateY(0);
}

.follow-up-actions {
  padding-top: var(--space-2);
  border-top: 1px solid var(--border-secondary);
}

/* System context messages */
.system-context-message .message-content {
  background-color: var(--color-info-light);
  border: 1px solid var(--color-info);
  color: var(--color-info-dark);
}

.system-context {
  font-style: italic;
  font-size: 0.9rem;
}

/* Enhanced message styling */
.ai-message.system-context-message {
  margin-bottom: var(--space-3);
  opacity: 0.9;
}

.ai-message.system-context-message .message-avatar {
  background-color: var(--color-info-light);
  border-color: var(--color-info);
}

/* Chat follow-up suggestions */
.suggestions-message .message-content {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
}

.suggestions-content {
  padding: var(--space-3);
}

.chat-follow-up-suggestions {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.chat-suggestion-btn {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  line-height: 1.4;
}

.chat-suggestion-btn:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

.chat-suggestion-btn:active {
  transform: translateY(0);
}

.suggestions-message .message-avatar {
  background-color: var(--color-warning-light);
  border-color: var(--color-warning);
}

/* Animation for explanation reveal */
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
    padding-top: var(--space-4);
    padding-bottom: var(--space-4);
  }
}

/* Responsive adjustments for AI features */
@media (max-width: 768px) {
  .ai-features-tabs {
    flex-direction: column;
    gap: var(--space-1);
  }

  .ai-tab-btn {
    text-align: left;
    border-radius: var(--radius-md);
  }

  .ai-chat-messages {
    height: 300px;
  }

  .ai-chat-suggestions {
    flex-direction: column;
  }

  .message-content {
    margin-right: var(--space-3);
  }

  .ai-message.user-message .message-content {
    margin-left: var(--space-3);
  }

  .news-actions {
    flex-direction: column;
  }

  .briefing-controls {
    flex-direction: column;
  }

  .briefing-input-section {
    flex-direction: column;
    align-items: stretch;
  }

  .categorization-tags {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Print styles */
@media print {
  .sidebar,
  .control-panel,
  .pagination-controls,
  .toast-container,
  .loading-overlay,
  .ai-features-dashboard {
    display: none !important;
  }

  .main-content {
    max-width: 100%;
    padding: 0;
  }

  .news-item {
    break-inside: avoid;
    margin-bottom: var(--space-4);
  }

  .header {
    background: none !important;
    color: black !important;
  }
}
